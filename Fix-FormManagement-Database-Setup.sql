-- =============================================
-- Fix Form Management Database Setup
-- This script fixes the empty grid and disabled buttons issue
-- Execute this script in your PostgreSQL database
-- =============================================

-- 1. Create Forms Table if it doesn't exist
CREATE TABLE IF NOT EXISTS forms (
    form_id SERIAL PRIMARY KEY,
    form_name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255),
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_forms_name ON forms(form_name);
CREATE INDEX IF NOT EXISTS idx_forms_category ON forms(category);
CREATE INDEX IF NOT EXISTS idx_forms_active ON forms(is_active);

-- 2. Insert sample forms data
INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'FormManagement', 'Form Management', 'Administration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'FormManagement');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'UserManagement', 'User Management', 'Administration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'UserManagement');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'PermissionManagement', 'Permission Management', 'Administration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'PermissionManagement');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'EstimateForm', 'Estimate Management', 'Business', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'EstimateForm');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'ParametersForm', 'Parameters', 'Configuration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'ParametersForm');

-- 3. Ensure Users table exists with proper structure
CREATE TABLE IF NOT EXISTS users (
    user_id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    full_name VARCHAR(255),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    can_read BOOLEAN DEFAULT true,
    can_create BOOLEAN DEFAULT true,
    can_edit BOOLEAN DEFAULT true,
    can_delete BOOLEAN DEFAULT true,
    can_print BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Insert default admin user (ID=1) with full permissions
INSERT INTO users (user_id, username, password_hash, full_name, email, is_active, can_read, can_create, can_edit, can_delete, can_print)
SELECT 1, 'admin', 'admin123', 'System Administrator', '<EMAIL>', true, true, true, true, true, true
WHERE NOT EXISTS (SELECT 1 FROM users WHERE user_id = 1);

-- Reset sequence to ensure user_id starts from 2 for new users
SELECT setval('users_user_id_seq', GREATEST(1, (SELECT MAX(user_id) FROM users)));

-- 5. Create permission tables if they don't exist
CREATE TABLE IF NOT EXISTS user_permissions (
    permission_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    form_name VARCHAR(100) NOT NULL,
    can_read BOOLEAN DEFAULT false,
    can_create BOOLEAN DEFAULT false,
    can_edit BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    can_print BOOLEAN DEFAULT false,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, form_name)
);

CREATE TABLE IF NOT EXISTS role_permissions (
    permission_id SERIAL PRIMARY KEY,
    role_name VARCHAR(100) NOT NULL,
    form_name VARCHAR(100) NOT NULL,
    can_read BOOLEAN DEFAULT false,
    can_create BOOLEAN DEFAULT false,
    can_edit BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    can_print BOOLEAN DEFAULT false,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_name, form_name)
);

-- 6. Grant full permissions to admin user (ID=1) for FormManagement
INSERT INTO user_permissions (user_id, form_name, can_read, can_create, can_edit, can_delete, can_print)
SELECT 1, 'FormManagement', true, true, true, true, true
WHERE NOT EXISTS (SELECT 1 FROM user_permissions WHERE user_id = 1 AND form_name = 'FormManagement');

-- Grant permissions for other forms as well
INSERT INTO user_permissions (user_id, form_name, can_read, can_create, can_edit, can_delete, can_print)
SELECT 1, 'UserManagement', true, true, true, true, true
WHERE NOT EXISTS (SELECT 1 FROM user_permissions WHERE user_id = 1 AND form_name = 'UserManagement');

INSERT INTO user_permissions (user_id, form_name, can_read, can_create, can_edit, can_delete, can_print)
SELECT 1, 'PermissionManagement', true, true, true, true, true
WHERE NOT EXISTS (SELECT 1 FROM user_permissions WHERE user_id = 1 AND form_name = 'PermissionManagement');

INSERT INTO user_permissions (user_id, form_name, can_read, can_create, can_edit, can_delete, can_print)
SELECT 1, 'EstimateForm', true, true, true, true, true
WHERE NOT EXISTS (SELECT 1 FROM user_permissions WHERE user_id = 1 AND form_name = 'EstimateForm');

INSERT INTO user_permissions (user_id, form_name, can_read, can_create, can_edit, can_delete, can_print)
SELECT 1, 'ParametersForm', true, true, true, true, true
WHERE NOT EXISTS (SELECT 1 FROM user_permissions WHERE user_id = 1 AND form_name = 'ParametersForm');

-- 7. Verify the setup
SELECT 'Forms Table Data:' as info;
SELECT form_id, form_name, display_name, category, is_active FROM forms ORDER BY form_id;

SELECT 'Admin User Data:' as info;
SELECT user_id, username, full_name, can_read, can_create, can_edit, can_delete, can_print FROM users WHERE user_id = 1;

SELECT 'Admin User Permissions:' as info;
SELECT form_name, can_read, can_create, can_edit, can_delete, can_print FROM user_permissions WHERE user_id = 1 ORDER BY form_name;

-- Success message
SELECT 'Database setup completed successfully! Form Management should now work properly.' as result;
