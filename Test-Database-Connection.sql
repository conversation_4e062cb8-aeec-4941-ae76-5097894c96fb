-- =============================================
-- Test Database Connection and Form Management Setup
-- Run this to verify everything is working
-- =============================================

-- 1. Test basic connection
SELECT 'Database connection successful!' as status, NOW() as current_time;

-- 2. Check if forms table exists and has data
SELECT 'Forms table check:' as info;
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'forms') 
        THEN 'Forms table exists'
        ELSE 'Forms table MISSING - run Fix-FormManagement-Database-Setup.sql'
    END as forms_table_status;

-- 3. Count forms in database
SELECT 'Form count:' as info;
SELECT COUNT(*) as total_forms FROM forms WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'forms');

-- 4. Check if users table exists
SELECT 'Users table check:' as info;
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') 
        THEN 'Users table exists'
        ELSE 'Users table MISSING - run Fix-FormManagement-Database-Setup.sql'
    END as users_table_status;

-- 5. Check admin user (ID=1)
SELECT 'Admin user check:' as info;
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM users WHERE user_id = 1) 
        THEN 'Admin user exists'
        ELSE 'Admin user MISSING - run Fix-FormManagement-Database-Setup.sql'
    END as admin_user_status
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users');

-- 6. Check user permissions table
SELECT 'User permissions check:' as info;
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_permissions') 
        THEN 'User permissions table exists'
        ELSE 'User permissions table MISSING - run Fix-FormManagement-Database-Setup.sql'
    END as permissions_table_status;

-- 7. Check FormManagement permissions for admin user
SELECT 'FormManagement permissions check:' as info;
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM user_permissions WHERE user_id = 1 AND form_name = 'FormManagement' AND can_read = true) 
        THEN 'Admin has FormManagement permissions'
        ELSE 'Admin MISSING FormManagement permissions - run Fix-FormManagement-Database-Setup.sql'
    END as formmanagement_permissions_status
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_permissions');

-- 8. Show current forms data (if table exists)
SELECT 'Current forms in database:' as info;
SELECT form_id, form_name, display_name, category, is_active 
FROM forms 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'forms')
ORDER BY form_id;

-- 9. Show admin user global permissions (if table exists)
SELECT 'Admin user global permissions:' as info;
SELECT username, can_read, can_create, can_edit, can_delete, can_print 
FROM users 
WHERE user_id = 1 AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users');

-- 10. Show admin user form-specific permissions (if table exists)
SELECT 'Admin user form-specific permissions:' as info;
SELECT form_name, can_read, can_create, can_edit, can_delete, can_print 
FROM user_permissions 
WHERE user_id = 1 AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_permissions')
ORDER BY form_name;
